{"name": "obsidian-to-anki-plugin", "version": "3.6.0", "description": "This is an Anki integration plugin! Designed for efficient bulk exporting.", "main": "main.js", "scripts": {"dev": "rollup --config rollup.config.js -w", "build": "rollup --config rollup.config.js", "copy": "rm -rf tests/defaults/test_vault/.obsidian/plugins/obsidian-to-anki-plugin && mkdir -p tests/defaults/test_vault/.obsidian/plugins/obsidian-to-anki-plugin && cp manifest.json styles.css main.js tests/defaults/test_vault/.obsidian/plugins/obsidian-to-anki-plugin/", "prep-wdio": "bash prepare-wdio.sh", "test-wdio": "npm run prep-wdio && docker build -t anki-obsidian . && wdio run ./wdio.conf.ts", "test-py": "pip install pytest anki && pytest -vvvs tests/anki/ --junitxml logs/test-reports/pytest.xml", "test": "npm run test-wdio && npm run test-py"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "15.1.0", "@rollup/plugin-node-resolve": "9.0.0", "@rollup/plugin-typescript": "^11.1.5", "@types/node": "^20.8.2", "@types/showdown": "^2.0.0", "@wdio/cli": "^8.6.9", "@wdio/junit-reporter": "^8.12.2", "@wdio/local-runner": "^8.6.9", "@wdio/mocha-framework": "^8.16.17", "@wdio/spec-reporter": "^8.12.2", "glob": "^10.3.10", "obsidian": "^1.4.11", "rollup": "2.79.1", "rollup-plugin-node-polyfills": "^0.2.1", "showdown-highlight": "^3.1.0", "ts-node": "^10.9.1", "tslib": "^2.6.1", "typescript": "^5.2.2", "wdio-chromedriver-service": "^8.1.1", "wdio-docker-service": "^3.2.1", "wdio-wait-for": "^3.0.7"}, "dependencies": {"byte-base64": "^1.1.0", "multimatch": "^7.0.0", "showdown": "^2.1.0", "ts-md5": "^1.2.7", "webdriver": "^8.5.5"}}